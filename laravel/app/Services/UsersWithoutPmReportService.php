<?php

namespace App\Services;

use App\Interfaces\Alerts\Fetchable;
use App\User;
use App\Traits\Reports\UserPerShiftReport;

class UsersWithoutPmReportService implements Fetchable
{
    use UserPerShiftReport;

    private const above = '23:01:00';

    private const below = '12:00:00';

    public function fetch(User $user, $from, $to): array
    {
        $allBelowUserIds = $this->getAllBelowUsersIds(
            $this->getLines($user, $from, $to),
            $user,
            $from,
            $to
        );

        $withoutVisits = $this->getUsersWithoutVisits($allBelowUserIds, $from, $to, self::above, self::below);

        $vacationUsers = $this->getUsersOnVacation($from, $to, 2); // PM shift = 2

        $withoutVisitsAndVacations = array_diff($withoutVisits, $vacationUsers);

        $officeWorkUsers = $this->getOfficeWorkUsers($from, $to, self::above, self::below);

        $userPositions = $this->getUsersOnPositions();

        $userOfHighLevel = $this->getUsersOnHighLevel();

        return array_diff($withoutVisitsAndVacations, $officeWorkUsers, $userPositions, $userOfHighLevel);
    }


}
