<?php

namespace App\Services;


use App\Interfaces\Alerts\Fetchable;
use App\User;
use App\Traits\Reports\UserPerShiftReport;

class UsersWithoutAmReportService implements Fetchable
{
    use UserPerShiftReport;

    private const above = '10:31:00';
    private const below = '08:00:00';

    public function fetch(User $user, string $from, string $to): array
    {
        $allBelowUserIds = $this->getAllBelowUsersIds(
            $this->getLines($user, $from, $to),
            $user,
            $from,
            $to
        );

        $withoutVisits = $this->getUsersWithoutVisits($allBelowUserIds, $from, $to, self::above, self::below);

        $vacationUsers = $this->getUsersOnVacation($from, $to, 1); // AM shift = 1

        $withoutVisitsAndVacations = array_diff($withoutVisits, $vacationUsers);

        $officeWorkUsers = $this->getOfficeWorkUsers($from, $to, self::above, self::below);

        $userPositions = $this->getUsersOnPositions();

        $userOfHighLevel = $this->getUsersOnHighLevel();

        return array_diff($withoutVisitsAndVacations, $officeWorkUsers,$userPositions,$userOfHighLevel);

    }

}
