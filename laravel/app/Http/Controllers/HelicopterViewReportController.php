<?php

namespace App\Http\Controllers;


use App\Services\Reports\HelicopterViewReportService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * Controller for helicopter view reports
 *
 * Provides comprehensive metrics including total accounts, visit coverage,
 * call rate, frequency, sales quantities, and targets for specified lines.
 */
class HelicopterViewReportController extends ApiController
{
    /**
     * @var HelicopterViewReportService
     */
    private HelicopterViewReportService $helicopterViewReportService;

    /**
     * Constructor with dependency injection
     */
    public function __construct(HelicopterViewReportService $helicopterViewReportService)
    {
        $this->helicopterViewReportService = $helicopterViewReportService;
    }

    /**
     * Generate helicopter view report
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function filter(Request $request): JsonResponse
    {
        try {
            // Validate request parameters
            $validator = $this->validateRequest($request);

            if ($validator->fails()) {
                Log::warning('Helicopter view report validation failed', [
                    'errors' => $validator->errors()->toArray(),
                    'user_id' => Auth::id()
                ]);

                return $this->respondError(
                    $validator->errors()->toArray(),
                    'Validation failed',
                    422
                );
            }

            $validated = $validator->validated();

            // Parse dates
            $fromDate = Carbon::parse($validated['from_date'])->startOfDay();
            $toDate = Carbon::parse($validated['to_date'])->endOfDay();

            // Validate date range
            if ($fromDate->gt($toDate)) {
                return $this->respondWithErrorMessage(
                    'Invalid date range: from_date must be before or equal to to_date',
                    400
                );
            }

            // Validate date range is not too large (optional business rule)
            $daysDifference = $fromDate->diffInDays($toDate);
            if ($daysDifference > 365) {
                return $this->respondWithErrorMessage(
                    'Date range too large: maximum 365 days allowed',
                    400
                );
            }

            $lineIds = $validated['line_ids'];
            $user = Auth::user();

            $includeSummary = $validated['include_summary'];
            $includeTrends = $validated['include_trends'];

            Log::info('Processing helicopter view report request', [
                'from_date' => $fromDate->toDateString(),
                'to_date' => $toDate->toDateString(),
                'line_ids' => $lineIds,
                'user_id' => $user->id,
                'days_range' => $daysDifference
            ]);

            // Generate report
            $reportData = $this->helicopterViewReportService->generateReport(
                $fromDate,
                $toDate,
                $lineIds,
                $user,
                $includeSummary,
                $includeTrends
            );

            Log::info('Helicopter view report generated successfully', [
                'user_id' => $user->id,
                'lines_count' => count($lineIds),
            ]);

            return $this->respond($reportData);

        } catch (\Exception $e) {
            Log::error('Error generating helicopter view report', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => Auth::id(),
                'request_data' => $request->all()
            ]);

            return $this->respondWithErrorMessage(
                'An error occurred while generating the report: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get available lines for the authenticated user
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getLines(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            Log::info('Fetching available lines for helicopter view report', [
                'user_id' => $user->id
            ]);

            // Get user's accessible lines
            $lines = $user->userLines();

            $linesData = $lines->map(function ($line) {
                return [
                    'id' => $line->id,
                    'name' => $line->name
                ];
            })->toArray();

            return $this->respond([
                'lines' => $linesData
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching lines for helicopter view report', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => Auth::id()
            ]);

            return $this->respondWithErrorMessage(
                'An error occurred while fetching lines: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * Validate the request parameters
     *
     * @param Request $request
     * @return \Illuminate\Contracts\Validation\Validator
     */
    private function validateRequest(Request $request): \Illuminate\Contracts\Validation\Validator
    {
        return Validator::make($request->all(), [
            'from_date' => [
                'required',
                'date',
                'before_or_equal:to_date'
            ],
            'to_date' => [
                'required',
                'date',
                'after_or_equal:from_date'
            ],
            'line_ids' => [
                'required',
                'array',
                'min:1'
            ],
            'line_ids.*' => [
                'required',
                'integer',
                'exists:lines,id'
            ],
            'include_summary' => [
                'required',
                'boolean'
            ],
            'include_trends' => [
                'required',
                'boolean'
            ]
        ], [
            'from_date.required' => 'From date is required',
            'from_date.date' => 'From date must be a valid date',
            'from_date.before_or_equal' => 'From date must be before or equal to to_date',
            'to_date.required' => 'To date is required',
            'to_date.date' => 'To date must be a valid date',
            'to_date.after_or_equal' => 'To date must be after or equal to from_date',
            'line_ids.required' => 'Line IDs are required',
            'line_ids.array' => 'Line IDs must be an array',
            'line_ids.min' => 'At least one line ID is required',
            'line_ids.*.required' => 'Each line ID is required',
            'line_ids.*.integer' => 'Each line ID must be an integer',
            'line_ids.*.exists' => 'One or more line IDs do not exist',
            'include_summary.required' => 'Include summary is required',
            'include_summary.boolean' => 'Include summary must be a boolean value',
            'include_trends.required' => 'Include trends is required',
            'include_trends.boolean' => 'Include trends must be a boolean value'
        ]);
    }
}
